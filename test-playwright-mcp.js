// Test script to verify Playwright MCP server functionality
const { spawn } = require('child_process');

console.log('Testing Playwright MCP Server...');

// Start the MCP server
const mcpServer = spawn('npx', ['-y', '@executeautomation/playwright-mcp-server'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

// Send an MCP initialization request
const initRequest = {
  jsonrpc: "2.0",
  id: 1,
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: {
      tools: {}
    },
    clientInfo: {
      name: "test-client",
      version: "1.0.0"
    }
  }
};

console.log('Sending initialization request...');
mcpServer.stdin.write(JSON.stringify(initRequest) + '\n');

// Listen for server response
mcpServer.stdout.on('data', (data) => {
  console.log('Server response:', data.toString());
});

mcpServer.stderr.on('data', (data) => {
  console.log('Server error:', data.toString());
});

// Clean up after 5 seconds
setTimeout(() => {
  console.log('Terminating test...');
  mcpServer.kill();
}, 5000);