/* Clean & Simple CSS for Eldergrove Energy Website */

:root {
    --primary-color: #22c55e;
    --secondary-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --text-color: #374151;
    --text-light: #6b7280;
    --text-lighter: #9ca3af;
    --white: #ffffff;
    --border-light: #e5e7eb;
    --gradient: linear-gradient(135deg, #22c55e 0%, #3b82f6 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Header Styles */
.header-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-light);
}

.fixed-top {
    position: fixed !important;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar {
    padding: 1rem 0;
    background-color: white !important;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--text-color) !important;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-light .navbar-toggler {
    border-color: var(--border-light);
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.brand-logo {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    background: transparent;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler i {
    color: var(--text-color);
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 0.75rem;
    transition: color 0.3s ease;
    text-decoration: none;
    border-radius: 5px;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color);
    background: var(--light-color);
}

.btn-cta {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-left: 1rem;
    display: inline-block;
}

.btn-cta:hover {
    background: #16a34a;
    color: var(--white);
    transform: translateY(-1px);
}

/* Navigation Dropdown Styles */
.navbar-nav .dropdown-menu {
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 200px;
}

.navbar-nav .dropdown-item {
    color: var(--text-color);
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0;
}

.navbar-nav .dropdown-item:hover,
.navbar-nav .dropdown-item:focus {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-nav .dropdown-toggle::after {
    margin-left: 0.5rem;
    vertical-align: 0.125em;
}

.navbar-nav .dropdown-toggle:hover::after {
    border-top-color: var(--primary-color);
}

/* Navigation CTA Button */
.nav-cta {
    background: var(--primary-color) !important;
    color: var(--white) !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.nav-cta:hover {
    background: #16a34a !important;
    color: var(--white) !important;
    transform: translateY(-1px) !important;
}

/* Hero Section */
.hero-section {
    min-height: 60vh;
    display: flex;
    align-items: center;
    background: var(--white);
    padding: 140px 0 80px;
    position: relative;
}

/* Hero Section with Background Image */
.hero-with-bg {
    position: relative;
    overflow: hidden;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.1;
}

.hero-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.6);
}

.hero-with-bg .container {
    position: relative;
    z-index: 2;
}

/* Hero Text Styles for Background Version */
.hero-title-white {
    color: var(--white) !important;
}

.hero-description-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.text-white {
    color: var(--white) !important;
}

.hero-with-bg .hero-title {
    color: var(--white) !important;
}

.hero-with-bg .hero-description {
    color: rgba(255, 255, 255, 0.95) !important;
}

.hero-with-bg .stat-item .stat-number {
    color: var(--white) !important;
}

.hero-with-bg .stat-item .stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

.hero-with-bg .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
    backdrop-filter: blur(10px);
}

.hero-content {
    padding: 2rem 0;
}

.hero-badge {
    display: inline-block;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.hero-subscription {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-style: italic;
}

.text-gradient {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-description-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #16a34a;
    color: var(--white);
    transform: translateY(-1px);
}

.btn-secondary {
    color: var(--white);
    background: var(--secondary-color);
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    color: var(--white);
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wind-turbine-graphic {
    position: relative;
    width: 200px;
    height: 400px;
}

.turbine-base {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: var(--text-light);
    border-radius: 50%;
}

.turbine-tower {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 300px;
    background: var(--text-light);
}

.turbine-nacelle {
    position: absolute;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 15px;
    background: var(--primary-color);
    border-radius: 10px;
}

.turbine-blades {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    animation: rotate 4s linear infinite;
    transform-origin: center center;
}

.blade {
    position: absolute;
    width: 2px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 2px;
    transform-origin: bottom center;
}

.blade-1 { transform: rotate(0deg); }
.blade-2 { transform: rotate(120deg); }
.blade-3 { transform: rotate(240deg); }

@keyframes rotate {
    0% { transform: translateX(-50%) rotate(0deg); }
    100% { transform: translateX(-50%) rotate(360deg); }
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    animation: float 4s ease-in-out infinite;
}

.element-1 {
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.element-2 {
    bottom: 30%;
    left: 10%;
    animation-delay: 1s;
}

.element-3 {
    top: 60%;
    right: 40%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Hero Image Slider */
.hero-slider {
    position: relative;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
    position: relative;
}

.hero-slide-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-slide-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.1;
}

.hero-slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.6);
}

.hero-slider-controls {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 3;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background: var(--white);
    transform: scale(1.2);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    z-index: 3;
    backdrop-filter: blur(10px);
}

.slider-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 2rem;
}

.slider-next {
    right: 2rem;
}

/* Section Styles */
.section-header {
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-block;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.about-content {
    padding: 2rem 0;
}

.about-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.about-icon {
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.about-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.about-text h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.about-text p {
    color: var(--text-light);
    line-height: 1.6;
}

.about-visual {
    position: relative;
}

.about-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(34, 197, 94, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.about-image:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: var(--white);
}

.overlay-content h5 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.overlay-content p {
    font-size: 1.125rem;
}

/* Services Section */
.services-section {
    padding: 5rem 0;
    background: var(--white);
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.service-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.service-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    padding: 0;
}

.service-features li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: 600;
}

/* Projects Section */
.projects-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.project-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--border-light);
}

.project-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.project-status {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.project-status.under-construction {
    background: #f59e0b;
}

.project-status.planning {
    background: var(--secondary-color);
}

.project-content {
    padding: 1.5rem;
}

.project-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.project-specs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-specs span {
    font-size: 0.875rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.project-specs i {
    color: var(--primary-color);
}

.project-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: gap 0.3s ease;
}

.project-link:hover {
    gap: 0.75rem;
    color: var(--primary-color);
}

/* Technology Section */
.technology-section {
    padding: 5rem 0;
    background: var(--white);
}

.technology-content {
    padding: 2rem 0;
}

.tech-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.tech-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.tech-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.tech-info h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.tech-info p {
    color: var(--text-light);
    line-height: 1.6;
}

.technology-visual {
    position: relative;
}

.technology-visual img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
}

.tech-stats {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    display: flex;
    gap: 2rem;
}

.tech-stat {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-desc {
    font-size: 0.875rem;
    color: var(--text-light);
/* Technology Feature Button */
.tech-feature-button {
    padding: 1rem 0;
    margin-top: 1rem;
}

.tech-feature-button .btn-primary {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2);
    transition: all 0.3s ease;
}

.tech-feature-button .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.tech-feature-button .btn-primary i {
    margin-right: 0.5rem;
}
}

/* Sustainability Section */
.sustainability-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.sustainability-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.sustainability-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.sustain-icon {
    width: 80px;
    height: 80px;
    background: var(--light-color);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.sustain-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.sustainability-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.sustainability-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.sustain-metric {
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* News Section */
.news-section {
    padding: 5rem 0;
    background: var(--white);
}

/* Additional Hero Section */
.additional-hero-section {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 80px 0 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.7);
}

.additional-hero-section .container {
    position: relative;
    z-index: 2;
}

.additional-hero-section .hero-content {
    color: var(--white) !important;
    text-align: center;
}

.additional-hero-section .hero-title {
    color: var(--white) !important;
}

.additional-hero-section .hero-description {
    color: rgba(255, 255, 255, 0.95) !important;
}

.additional-hero-section .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
    backdrop-filter: blur(10px);
}

.additional-hero-section .hero-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    backdrop-filter: blur(10px);
}

.additional-hero-section .hero-title {
    color: var(--white);
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.additional-hero-section .text-gradient {
    background: linear-gradient(135deg, #22c55e 0%, #60a5fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.additional-hero-section .hero-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.additional-hero-section .hero-actions {
    justify-content: center;
}

/* Impact Hero Section */
.impact-hero-section {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 80px 0 80px;
}

.impact-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.impact-hero-section .hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.impact-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(249, 250, 251, 0.85);
}

.impact-hero-section .container {
    position: relative;
    z-index: 2;
}

.impact-hero-section .hero-content {
    color: var(--dark-color);
    text-align: center;
}

.impact-hero-section .hero-badge {
    background: rgba(34, 197, 94, 0.1);
    color: var(--primary-color);
    backdrop-filter: blur(10px);
}

.impact-hero-section .hero-title {
    color: var(--dark-color);
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.impact-hero-section .text-gradient {
    background: linear-gradient(135deg, #22c55e 0%, #60a5fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.impact-hero-section .hero-description {
    color: var(--text-color);
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Testimonials Section */
.testimonials-section {
    padding: 5rem 0;
    background: var(--white);
    font-family: 'Poppins', sans-serif;
}

.testimonial-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    font-family: 'Poppins', sans-serif;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.quote-icon {
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.quote-icon i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.testimonial-content p {
    color: var(--text-color);
    font-style: italic;
    line-height: 1.6;
    font-size: 1.125rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--light-color);
}

.author-info h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Global Presence Section */
.global-presence-section {
    padding: 5rem 0;
    background: var(--light-color);
}

/* Global Impact Cards */
.global-impact-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.global-impact-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.impact-icon {
    width: 80px;
    height: 80px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.impact-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.impact-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.impact-description {
    color: var(--text-light);
    font-size: 0.95rem;
    margin: 0;
}

.global-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.stat-item-large {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-item-large:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.stat-item-large .stat-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-item-large .stat-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-content .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-content .stat-label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-content .stat-description {
    font-size: 0.875rem;
    color: var(--text-light);
    margin: 0;
}



.news-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-date {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--white);
    padding: 0.5rem;
    border-radius: 10px;
    text-align: center;
    min-width: 50px;
}

.date-day {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.date-month {
    font-size: 0.75rem;
    color: var(--text-light);
    text-transform: uppercase;
}

.news-content {
    padding: 1.5rem;
}

.news-category {
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.news-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.news-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: gap 0.3s ease;
}

.news-link:hover {
    gap: 0.75rem;
    color: var(--primary-color);
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.contact-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.contact-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

/* Contact Info Card */
.contact-info-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.contact-info-card h4 {
    color: var(--dark-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
}

.contact-info-card h4:first-child {
    margin-top: 0;
}

.contact-info-card h4 i {
    color: var(--primary-color);
}

.contact-info-card p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.contact-info-card p:last-child {
    margin-bottom: 0;
}

.contact-info-card strong {
    color: var(--dark-color);
}

/* Contact Info Section */
.contact-info-section {
    padding: 2rem 0;
}

.contact-info-section h4 {
    color: var(--dark-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.contact-info-section h4 i {
    color: var(--primary-color);
}

.contact-details p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.contact-details p:last-child {
    margin-bottom: 0;
}

.contact-details strong {
    color: var(--dark-color);
}

.contact-form {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
}

.contact-form .form-control {
    border: 1px solid var(--border-light);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.1);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 5rem 0 2rem;
}

.footer-brand h5 {
    color: var(--white);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: var(--text-lighter);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--white);
}

.footer-links h6 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: var(--text-lighter);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    margin-top: 2rem;
}

.footer-bottom p {
    color: var(--text-lighter);
    margin: 0;
}

.footer-bottom a {
    color: var(--text-lighter);
    text-decoration: none;
    margin-left: 1rem;
    transition: color 0.3s ease;
}

.footer-bottom a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .about-item,
    .tech-feature {
        flex-direction: column;
        text-align: center;
    }

    .about-icon,
    .tech-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .hero-visual {
        height: 300px;
        margin-top: 2rem;
    }

    .wind-turbine-graphic {
        width: 150px;
        height: 300px;
    }

    .tech-stats {
        position: static;
        justify-content: center;
        margin-top: 1rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
        padding: 0.75rem 1rem;
        border-radius: 5px;
    }

    .btn-cta {
        margin-left: 0;
        margin-top: 1rem;
        display: block;
        text-align: center;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
    }

    .navbar-nav .dropdown-menu {
        border: none;
        box-shadow: none;
        background: var(--light-color);
        margin-top: 0;
        padding: 0;
        border-radius: 0;
    }

    .navbar-nav .dropdown-item {
        padding: 0.5rem 2rem;
        font-size: 0.9rem;
    }

    .navbar-collapse {
        background: var(--white);
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid var(--border-light);
    }

    /* Hero Sections Mobile */
    .hero-section {
        min-height: 55vh;
        padding: 80px 0 30px;
    }

    .additional-hero-section {
        min-height: 45vh;
        padding: 50px 0 30px;
    }

    .impact-hero-section {
        min-height: 45vh;
        padding: 50px 0 30px;
    }

    .additional-hero-section .hero-title,
    .impact-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .additional-hero-section .hero-description,
    .impact-hero-section .hero-description {
        font-size: 1.125rem;
    }

    /* Global Presence Mobile */
    .global-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-item-large {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    /* Testimonials Mobile */
    .testimonial-author {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .testimonial-content p {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .hero-stats {
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* Hero Sections Small Mobile */
    .hero-section {
        min-height: 50vh;
        padding: 60px 0 25px;
    }

    .additional-hero-section {
        min-height: 40vh;
        padding: 40px 0 25px;
    }

    .impact-hero-section {
        min-height: 40vh;
        padding: 40px 0 25px;
    }

    .additional-hero-section .hero-title,
    .impact-hero-section .hero-title {
        font-size: 2rem;
    }

    .additional-hero-section .hero-description,
    .impact-hero-section .hero-description {
        font-size: 1rem;
    }

    /* Global Presence Small Mobile */
    .stat-item-large {
        padding: 1.5rem;
    }

    .stat-content .stat-number {
        font-size: 1.75rem;
    }

    /* Testimonials Small Mobile */
    .testimonial-card {
        padding: 1.5rem;
    }

    .testimonial-content p {
        font-size: 0.95rem;
    }

    .author-image {
        width: 50px;
        height: 50px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Background Classes for Section Alternation */
.bg-white {
    background-color: var(--white) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.mt-5 {
    margin-top: 3rem;
}

.py-5 {
    padding: 3rem 0;
}

/* Focus States for Accessibility */
.btn-primary:focus,
.btn-secondary:focus,
.btn-cta:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.form-control:focus {
    outline: none;
}

/* Print Styles */
@media print {
    .header-nav,
    .footer {
        display: none;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    * {
        background: white !important;
        color: black !important;
    }
}
