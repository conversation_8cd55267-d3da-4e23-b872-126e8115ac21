/* Clean Slider CSS - No Conflicts */

/* Variables */
:root {
    --primary-color: #28a745;
    --secondary-color: #20c997;
    --dark-color: #2c3e50;
    --text-color: #495057;
    --text-light: #6c757d;
    --light-color: #f8f9fa;
    --white: #ffffff;
    --border-light: #e9ecef;
}

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

/* Header */
.header-nav {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.brand-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-cta {
    background: var(--primary-color);
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
}

/* Hero Slider */
.hero-slider {
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 180px 0 120px;
    overflow: hidden;
}

.hero-slide.active {
    opacity: 1;
    position: relative;
}

.hero-slide-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-slide-bg-img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    opacity: 0.8 !important;
    filter: brightness(0.7) !important;
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
}

.hero-slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.4) 0%, rgba(32, 201, 151, 0.3) 100%);
}

.hero-slide .container {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Hero Content */
.hero-main-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.text-gradient {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animated Text */
.animated-text {
    position: relative;
    display: inline-block;
    height: 1.2em;
    overflow: hidden;
    vertical-align: baseline;
}

.text-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    white-space: nowrap;
}

.text-item.active {
    opacity: 1;
    transform: translateY(0);
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    padding: 14px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    margin: 0 8px;
    border: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    color: white;
    text-decoration: none;
}

.btn-outline-white {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 14px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    margin: 0 8px;
}

.btn-outline-white:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    text-decoration: none;
}

/* Statistics Cards - Removed for cleaner slider design */

/* Slider Controls */
.hero-slider-controls {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 10;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background: white;
    transform: scale(1.2);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.slider-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 30px;
}

.slider-next {
    right: 30px;
}

/* Sections */
.section-header {
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-main-title {
        font-size: 2.5rem;
        line-height: 1.1;
        margin-bottom: 1rem;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }



    .slider-nav {
        width: 40px;
        height: 40px;
    }

    .slider-prev {
        left: 15px;
    }

    .slider-next {
        right: 15px;
    }

    .hero-slide {
        padding: 140px 0 80px;
    }
}

@media (max-width: 480px) {
    .hero-main-title {
        font-size: 2rem;
    }



    .btn-primary, .btn-outline-white {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* About Page Styles */
.about-hero-section {
    position: relative;
    overflow: hidden;
    min-height: 50vh;
    display: flex;
    align-items: center;
    padding: 120px 0 80px;
}

.about-hero-section .container {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* About Statistics Section */
.about-stats-section {
    padding: 4rem 0;
    margin-top: -2rem;
    position: relative;
    z-index: 3;
}

.about-stat-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border-top: 4px solid var(--primary-color);
}

.about-stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.about-stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.about-stat-card .stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.about-stat-card .stat-description {
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.5;
}

/* Story Section */
.our-story-section {
    padding: 5rem 0;
}

.story-timeline {
    margin-top: 2rem;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-light);
}

.timeline-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.timeline-year {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    min-width: 80px;
    text-align: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.timeline-content h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-content p {
    color: var(--text-light);
    margin: 0;
}

.story-visual {
    position: relative;
}

.story-stats {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    justify-content: space-around;
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Values Section */
.our-values-section {
    padding: 5rem 0;
}

.value-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-icon i {
    font-size: 2rem;
    color: white;
}

.value-card h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Impact Section */
.our-impact-section {
    padding: 5rem 0;
}

.impact-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.impact-card:hover {
    transform: translateY(-5px);
}

.impact-icon {
    width: 70px;
    height: 70px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.impact-icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.impact-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.impact-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.impact-description {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Leadership Section */
.leadership-section {
    padding: 5rem 0;
}

.leader-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.leader-card:hover {
    transform: translateY(-5px);
}

.leader-image {
    height: 250px;
    overflow: hidden;
}

.leader-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.leader-card:hover .leader-image img {
    transform: scale(1.05);
}

.leader-content {
    padding: 2rem;
    text-align: center;
}

.leader-content h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.leader-position {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
    display: block;
    margin-bottom: 1rem;
}

.leader-content p {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.leader-social {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.leader-social .social-link {
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.leader-social .social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* About Page Responsive */
@media (max-width: 768px) {
    .about-hero-section {
        padding: 100px 0 60px;
        min-height: 40vh;
    }

    .about-stats-section {
        padding: 3rem 0;
        margin-top: -1rem;
    }

    .about-stat-card {
        padding: 2rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .about-stat-card .stat-number {
        font-size: 2rem;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
    }

    .timeline-year {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .story-stats {
        position: static;
        margin-top: 1rem;
        background: white;
    }

    .value-card, .impact-card, .leader-card {
        margin-bottom: 2rem;
    }
}
